<template>
  <UDialog v-model:open="isOpen" :una="{ dialogContent: 'md:max-w-md' }">
    <div>
      <div class="mb-3">
        <h3 class="font-title uppercase text-size-4 font-600 ml-4">Add New Quote</h3>
      </div>

      <form @submit.prevent="submitQuote" class="space-y-6">
        <div>
          <UInput
            type="textarea"
            v-model="form.content"
            class="text-size-6 font-600 font-subtitle border-dashed 
              focus-visible:border-gray-700 ring-transparent dark:focus-visible:ring-transparent dark:focus-visible:border-gray-300"
            placeholder="Enter the quote content..."
            :rows="4"
            :disabled="submitting"
            required
          />
        </div>

        <!-- Author Search -->
        <div>
          <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
            Author
          </label>
          <div class="relative">
            <UInput
              v-model="authorQuery"
              placeholder="Search for an author or enter a new one..."
              :disabled="submitting"
              @input="searchAuthors"
              @focus="showAuthorSuggestions = true"
            />
            <!-- Author Suggestions -->
            <div
              v-if="showAuthorSuggestions && (authorSuggestions.length > 0 || authorQuery)"
              class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-48 overflow-auto"
            >
              <div
                v-for="author in authorSuggestions"
                :key="author.id"
                class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center space-x-2"
                @click="selectAuthor(author)"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium">{{ author.name }}</div>
                  <div v-if="author.job" class="text-xs text-gray-500">{{ author.job }}</div>
                </div>
              </div>
              <div
                v-if="authorQuery && !authorSuggestions.some(a => a.name.toLowerCase() === authorQuery.toLowerCase())"
                class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-t border-gray-200 dark:border-gray-700"
                @click="createNewAuthor"
              >
                <div class="text-sm font-medium text-blue-600 dark:text-blue-400">
                  Create new author: "{{ authorQuery }}"
                </div>
              </div>
            </div>
          </div>
          <!-- Selected Author Display -->
          <div v-if="form.selectedAuthor" class="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-between">
            <div>
              <span class="text-sm font-medium">{{ form.selectedAuthor.name }}</span>
              <span v-if="form.selectedAuthor.job" class="text-xs text-gray-500 ml-2">{{ form.selectedAuthor.job }}</span>
            </div>
            <UButton
              size="xs"
              btn="ghost"
              icon
              label="i-ph-x"
              @click="clearAuthor"
            />
          </div>
        </div>

        <!-- Reference Search -->
        <div>
          <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
            Reference
          </label>
          <div class="relative">
            <UInput
              v-model="referenceQuery"
              placeholder="Search for a reference or enter a new one..."
              :disabled="submitting"
              @input="searchReferences"
              @focus="showReferenceSuggestions = true"
            />
            <!-- Reference Suggestions -->
            <div
              v-if="showReferenceSuggestions && (referenceSuggestions.length > 0 || referenceQuery)"
              class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-48 overflow-auto"
            >
              <div
                v-for="reference in referenceSuggestions"
                :key="reference.id"
                class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center space-x-2"
                @click="selectReference(reference)"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium">{{ reference.name }}</div>
                  <div v-if="reference.primary_type" class="text-xs text-gray-500 capitalize">{{ reference.primary_type.replace('_', ' ') }}</div>
                </div>
              </div>
              <div
                v-if="referenceQuery && !referenceSuggestions.some(r => r.name.toLowerCase() === referenceQuery.toLowerCase())"
                class="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-t border-gray-200 dark:border-gray-700"
                @click="createNewReference"
              >
                <div class="text-sm font-medium text-blue-600 dark:text-blue-400">
                  Create new reference: "{{ referenceQuery }}"
                </div>
              </div>
            </div>
          </div>
          <!-- Selected Reference Display -->
          <div v-if="form.selectedReference" class="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-between">
            <div>
              <span class="text-sm font-medium">{{ form.selectedReference.name }}</span>
              <span v-if="form.selectedReference.primary_type" class="text-xs text-gray-500 ml-2 capitalize">{{ form.selectedReference.primary_type.replace('_', ' ') }}</span>
            </div>
            <UButton
              size="xs"
              btn="ghost"
              icon
              label="i-ph-x"
              @click="clearReference"
            />
          </div>
        </div>

        <!-- Language -->
        <div>
          <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
            Language
          </label>
          <USelect
            v-model="form.language"
            :items="languageOptions"
            placeholder="Select language"
            :disabled="submitting"
            item-key="label"
            value-key="label"
          />
        </div>
      </form>

      <div class="mt-6 flex justify-end space-x-3">
        <UButton btn="light:soft dark:soft-white" @click="closeDialog" :disabled="submitting">
          Cancel
        </UButton>
        <UButton
          btn="soft-blue"
          :loading="submitting"
          @click="submitQuote"
          :disabled="!form.content.trim()"
        >
          Submit Quote
        </UButton>
      </div>
    </div>
  </UDialog>
</template>

<script setup lang="ts">
import type { Author } from '~/types/author'
import type { QuoteReference } from '~/types/quote-reference'
import type { QuoteLanguage } from '~/types/quote'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'quote-added'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { user } = useUserSession()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Form state
const form = ref({
  content: '',
  language: { label: 'English', value: 'en' },
  selectedAuthor: null as Author | null,
  selectedReference: null as QuoteReference | null
})

// Search state
const authorQuery = ref('')
const referenceQuery = ref('')
const authorSuggestions = ref<Author[]>([])
const referenceSuggestions = ref<QuoteReference[]>([])
const showAuthorSuggestions = ref(false)
const showReferenceSuggestions = ref(false)
const submitting = ref(false)

// Language options
const languageOptions = [
  { label: 'English', value: 'en' },
  { label: 'French', value: 'fr' },
  // { label: 'Spanish', value: 'es' },
  // { label: 'German', value: 'de' },
  // { label: 'Italian', value: 'it' },
  // { label: 'Portuguese', value: 'pt' },
  // { label: 'Russian', value: 'ru' },
  // { label: 'Japanese', value: 'ja' },
  // { label: 'Chinese', value: 'zh' }
]

// Search debounced functions
const searchAuthors = useDebounceFn(async () => {
  if (!authorQuery.value.trim()) {
    authorSuggestions.value = []
    return
  }

  try {
    const response = await $fetch('/api/authors/search', {
      query: { q: authorQuery.value, limit: 5 }
    })
    authorSuggestions.value = response.data || []
  } catch (error) {
    console.error('Error searching authors:', error)
    authorSuggestions.value = []
  }
}, 300)

const searchReferences = useDebounceFn(async () => {
  if (!referenceQuery.value.trim()) {
    referenceSuggestions.value = []
    return
  }

  try {
    const response = await $fetch('/api/references/search', {
      query: { q: referenceQuery.value, limit: 5 }
    })
    referenceSuggestions.value = response.data || []
  } catch (error) {
    console.error('Error searching references:', error)
    referenceSuggestions.value = []
  }
}, 300)

// Selection functions
const selectAuthor = (author: Author) => {
  form.value.selectedAuthor = author
  authorQuery.value = author.name
  showAuthorSuggestions.value = false
}

const selectReference = (reference: QuoteReference) => {
  form.value.selectedReference = reference
  referenceQuery.value = reference.name
  showReferenceSuggestions.value = false
}

const createNewAuthor = () => {
  form.value.selectedAuthor = {
    id: 0, // Will be created
    name: authorQuery.value,
    is_fictional: false,
    birth_date: null,
    birth_location: null,
    death_date: null,
    death_location: null,
    job: null,
    description: null,
    image_url: null,
    socials: '{}',
    views_count: 0,
    likes_count: 0,
    shares_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showAuthorSuggestions.value = false
}

const createNewReference = () => {
  form.value.selectedReference = {
    id: 0, // Will be created
    name: referenceQuery.value,
    original_language: form.value.language.value,
    release_date: null,
    description: null,
    primary_type: 'other',
    secondary_type: null,
    image_url: null,
    urls: '{}',
    views_count: 0,
    likes_count: 0,
    shares_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  showReferenceSuggestions.value = false
}

const clearAuthor = () => {
  form.value.selectedAuthor = null
  authorQuery.value = ''
}

const clearReference = () => {
  form.value.selectedReference = null
  referenceQuery.value = ''
}

const resetForm = () => {
  form.value = {
    content: '',
    language: { label: 'English', value: 'en' },
    selectedAuthor: null,
    selectedReference: null
  }
  authorQuery.value = ''
  referenceQuery.value = ''
  authorSuggestions.value = []
  referenceSuggestions.value = []
}

const closeDialog = () => {
  isOpen.value = false
  resetForm()
}

const submitQuote = async () => {
  if (!form.value.content.trim() || !user.value) return

  submitting.value = true
  try {
    const payload = {
      name: form.value.content.trim(),
      language: form.value.language.value,
      author_id: form.value.selectedAuthor?.id || null,
      reference_id: form.value.selectedReference?.id || null,
      user_id: user.value.id,
      status: 'draft' as const,
      // Include new author/reference data if needed
      new_author: form.value.selectedAuthor?.id === 0 ? {
        name: form.value.selectedAuthor.name,
        is_fictional: false
      } : null,
      new_reference: form.value.selectedReference?.id === 0 ? {
        name: form.value.selectedReference.name,
        original_language: form.value.language.value,
        primary_type: 'other' as const
      } : null
    }

    await $fetch('/api/quotes', {
      method: 'POST',
      body: payload
    })

    useToast().toast({
      toast: 'success',
      title: 'Quote Added',
      description: 'Your quote has been saved as a draft.'
    })

    emit('quote-added')
    closeDialog()
  } catch (error) {
    console.error('Error creating quote:', error)
    useToast().toast({
      toast: 'error',
      title: 'Error',
      description: 'Failed to add quote. Please try again.'
    })
  } finally {
    submitting.value = false
  }
}

// Click outside to close suggestions
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showAuthorSuggestions.value = false
    showReferenceSuggestions.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
